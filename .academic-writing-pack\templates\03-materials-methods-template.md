# 材料与方法模板总指导 (Materials and Methods Template Master Guide)

[[LLM: This master template provides comprehensive guidance for organizing materials and methods sections in academic papers. Based on analysis of high-impact publications and the minimally invasive medical device paper, it offers multiple organizational approaches and specialized sub-templates for different research types.]]

## 模板说明
- **用途**: 学术论文材料与方法部分的总体指导和子模板选择指南
- **适用范围**: 生物医学工程、技术创新、临床研究、产品开发、微创医疗技术等多领域
- **核心原则**: 系统性描述、制造可扩展性、材料特性详述、算法可重现性、临床可验证性
- **组织方式**: 根据研究类型选择相应的子模板组合
- **结构选项A**: 材料选择→制造工艺→器件表征→系统集成→性能验证（制造导向）
- **结构选项B**: 系统设计→功能实现→算法开发→临床协议→数据分析（功能导向）
- **写作风格**: 技术详实、工艺精确、参数明确、算法清晰、验证充分

## 📋 子模板选择指南

### **研究类型与推荐模板组合**

| 研究类型 | 主要内容 | 推荐子模板组合 | 适用期刊类型 |
|---------|---------|---------------|-------------|
| **材料工程研究** | 材料制备、工艺优化、性能表征 | 3.1 + 3.2 + 3.4 | Nature Materials, Advanced Materials |
| **生物医学设备** | 系统设计、临床验证、安全性评估 | 3.1 + 3.3 + 3.5 | Nature Medicine, Science Translational Medicine |
| **微创医疗技术** | 制备工艺、体内验证、统计分析 | 3.2 + 3.3 + 3.5 | Nature Biomedical Engineering |
| **算法开发研究** | 系统架构、算法实现、性能验证 | 3.1 + 3.6 | IEEE Transactions, Nature Machine Intelligence |
| **多学科交叉** | 综合系统、全面验证 | 3.1 + 3.2 + 3.4 + 3.5 | Science, Nature |

### **子模板详细说明**

#### **3.1 系统设计与实验方案** (`03.1-system-design-experimental-protocol.md`)
- **适用**: 系统架构设计、实验方案制定、研究流程规划
- **核心内容**: 系统整体架构、实验设计原理、研究方法学
- **关键要素**: 设计依据、技术路线、验证策略

#### **3.2 材料制备与制造工艺** (`03.2-materials-fabrication-processes.md`)
- **适用**: 材料选择、制备工艺、制造流程、质量控制
- **核心内容**: 材料规格、制备参数、工艺流程、可扩展性
- **关键要素**: 材料特性、工艺参数、制造精度

#### **3.3 临床研究与伦理协议** (`03.3-clinical-research-ethics-protocol.md`)
- **适用**: 临床试验设计、伦理审批、患者招募、数据采集
- **核心内容**: 研究协议、伦理合规、临床操作、安全监测
- **关键要素**: 伦理审批、知情同意、临床标准

#### **3.4 表征测试与分析方法** (`03.4-characterization-testing-analysis.md`)
- **适用**: 性能表征、机械测试、电学测试、生物相容性评估
- **核心内容**: 测试方法、设备参数、数据处理、结果分析
- **关键要素**: 测试标准、设备规格、分析方法

#### **3.5 统计分析与数据处理** (`03.5-statistical-analysis-data-processing.md`)
- **适用**: 数据统计、相关性分析、一致性评估、显著性检验
- **核心内容**: 统计方法、数据处理、结果验证、误差分析
- **关键要素**: 统计检验、置信区间、显著性水平

#### **3.6 算法实现与信号处理** (`03.6-algorithm-implementation-signal-processing.md`)
- **适用**: 算法开发、信号处理、机器学习、数据分析
- **核心内容**: 算法架构、实现细节、参数设置、性能优化
- **关键要素**: 算法原理、实现参数、验证方法

[[LLM: Provide both manufacturing-oriented and function-oriented organization options. Include detailed fabrication parameters, algorithm implementations, clinical protocols, and statistical methods. Ensure reproducibility through precise specifications.]]

## 🎯 使用指南

### **基础技术研究** → IEEE Transactions, Applied Physics Letters
```
主模板: 03 (总指导)
子模板: 3.1 + 3.2 + 3.4 + 3.6
重点: 系统设计 + 材料制备 + 性能表征 + 算法实现
```

### **生物医学设备** → Nature Medicine, Science Translational Medicine
```
主模板: 03 (总指导)
子模板: 3.1 + 3.3 + 3.4 + 3.5
重点: 系统设计 + 临床协议 + 安全性评估 + 统计分析
```

### **微创医疗技术** → Nature Biomedical Engineering
```
主模板: 03 (总指导)
子模板: 3.2 + 3.3 + 3.5
重点: 制备工艺 + 临床验证 + 统计分析
```

### **材料工程研究** → Nature Materials, Advanced Materials
```
主模板: 03 (总指导)
子模板: 3.2 + 3.4
重点: 材料制备 + 表征测试
```

### **算法开发研究** → Nature Machine Intelligence, IEEE Transactions
```
主模板: 03 (总指导)
子模板: 3.1 + 3.6 + 3.5
重点: 系统架构 + 算法实现 + 数据分析
```

## 📊 快速选择表

| 如果你的研究涉及... | 必选子模板 | 可选子模板 | 重点关注 |
|-------------------|-----------|-----------|---------|
| 新材料开发 | 3.2, 3.4 | 3.1 | 制备工艺、性能表征 |
| 医疗器械 | 3.1, 3.3, 3.5 | 3.2, 3.4 | 临床安全、统计验证 |
| 算法创新 | 3.1, 3.6 | 3.5 | 系统架构、算法细节 |
| 临床试验 | 3.3, 3.5 | 3.1, 3.4 | 伦理合规、数据分析 |
| 产品开发 | 3.1, 3.2 | 3.4, 3.6 | 系统集成、制造工艺 |

## 💡 写作建议

### 制造导向研究
1. 从材料选择开始 (3.2)
2. 详述制备工艺 (3.2)
3. 全面性能表征 (3.4)
4. 系统集成验证 (3.1)

### 功能导向研究
1. 系统设计原理 (3.1)
2. 功能实现方法 (3.6)
3. 临床验证协议 (3.3)
4. 数据分析方法 (3.5)

### 微创医疗技术
1. 生物相容材料制备 (3.2)
2. 临床安全性验证 (3.3)
3. 严谨统计分析 (3.5)
4. 监管合规要求 (3.3)

## 📝 模板使用说明

### 写作流程建议
1. **确定研究类型**：根据上述分类确定你的研究属于哪种类型
2. **选择子模板组合**：按照推荐组合选择相应的子模板
3. **按序组织内容**：按照子模板的逻辑顺序组织材料方法部分
4. **检查完整性**：使用质量检查清单确保内容完整
5. **优化表述**：根据目标期刊调整技术细节的深度

### 特殊研究类型指导

#### 微创医疗技术研究
- 重点强调生物相容性和安全性
- 详细描述制备工艺的精确参数
- 包含完整的临床验证协议
- 提供严谨的统计分析方法

#### 多技术集成研究
- 系统描述各技术模块的集成方式
- 强调协同效应的验证方法
- 详述系统级性能测试方案
- 体现技术创新的突破性

#### 算法驱动研究
- 清晰描述算法架构和实现细节
- 提供完整的参数设置和优化过程
- 强调算法的可重现性和泛化能力
- 包含充分的验证和对比实验





## 质量检查清单

### 系统描述完整性
- [ ] 系统架构和组件功能明确
- [ ] 制造工艺参数详细准确
- [ ] 材料规格和供应商信息完整
- [ ] 设备组装流程可重现
- [ ] 计算建模方法科学合理

### 实验设计规范性
- [ ] 机械表征方法标准化
- [ ] 临床研究协议伦理合规
- [ ] 数据采集参数明确
- [ ] 对照实验设计合理
- [ ] 统计分析方法恰当

### 技术创新突出性
- [ ] 可扩展制造工艺创新
- [ ] 软硬件集成技术先进
- [ ] 机器学习算法原创
- [ ] 临床验证方法严谨
- [ ] 性能指标评价全面

### 可重现性保障
- [ ] 制备工艺参数精确
- [ ] 测试条件标准化
- [ ] 数据处理流程清晰
- [ ] 算法实现细节完整
- [ ] 临床协议标准化
